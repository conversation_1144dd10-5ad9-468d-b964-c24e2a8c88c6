import { render, screen, userEvent, within } from 'tests/test-utils';
import createMatchMedia from 'tests/create-match-media';
import { IssueQualityIndicator } from '.';

const mockUseMediaQuery = jest.fn();

jest.mock('@shape-construction/hooks', () => ({
  useMediaQuery: jest.fn(() => mockUseMediaQuery()),
}));

const mockMessages = {
  'notUseful.title': 'Not useful',
  'notUseful.scoreRange': '0-19',
  'notUseful.description': 'This issue lacks essential information needed for action.',
  'theBasics.title': 'The basics',
  'theBasics.scoreRange': '20-29',
  'theBasics.description': 'This issue has basic information but could be improved.',
};

describe('<IssueQualityIndicator />', () => {
  beforeEach(() => {
    mockUseMediaQuery.mockReturnValue(true);
  });

  describe('when qualityScore is null', () => {
    it('renders an empty div', () => {
      const { container } = render(
        <IssueQualityIndicator qualityScore={null} className="test-class" />,
        { messages: { 'issue.list.qualityIndicators': mockMessages } }
      );

      expect(container.firstChild).toHaveClass('test-class');
      expect(container.firstChild).toBeEmptyDOMElement();
    });
  });

  describe('when qualityScore is 30 or above', () => {
    it('renders an empty div', () => {
      const { container } = render(
        <IssueQualityIndicator qualityScore={30} className="test-class" />,
        { messages: { 'issue.list.qualityIndicators': mockMessages } }
      );

      expect(container.firstChild).toHaveClass('test-class');
      expect(container.firstChild).toBeEmptyDOMElement();
    });
  });

  describe('when qualityScore is below 19 (danger threshold)', () => {
    it('renders the quality indicator with danger color', () => {
      render(
        <IssueQualityIndicator qualityScore={15} />,
        { messages: { 'issue.list.qualityIndicators': mockMessages } }
      );

      const button = screen.getByRole('button', { name: 'Issue quality: Not useful' });
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-describedby', 'quality-popover-content');
    });

    it('shows correct popover content when opened', async () => {
      render(
        <IssueQualityIndicator qualityScore={15} />,
        { messages: { 'issue.list.qualityIndicators': mockMessages } }
      );

      const button = screen.getByRole('button', { name: 'Issue quality: Not useful' });
      await userEvent.click(button);

      const popover = await screen.findByRole('dialog');
      expect(within(popover).getByText('Not useful')).toBeInTheDocument();
      expect(within(popover).getByText('0-19')).toBeInTheDocument();
      expect(within(popover).getByText('This issue lacks essential information needed for action.')).toBeInTheDocument();
    });
  });

  describe('when qualityScore is between 19 and 29 (warning threshold)', () => {
    it('renders the quality indicator with warning color', () => {
      render(
        <IssueQualityIndicator qualityScore={25} />,
        { messages: { 'issue.list.qualityIndicators': mockMessages } }
      );

      const button = screen.getByRole('button', { name: 'Issue quality: The basics' });
      expect(button).toBeInTheDocument();
    });

    it('shows correct popover content when opened', async () => {
      render(
        <IssueQualityIndicator qualityScore={25} />,
        { messages: { 'issue.list.qualityIndicators': mockMessages } }
      );

      const button = screen.getByRole('button', { name: 'Issue quality: The basics' });
      await userEvent.click(button);

      const popover = await screen.findByRole('dialog');
      expect(within(popover).getByText('The basics')).toBeInTheDocument();
      expect(within(popover).getByText('20-29')).toBeInTheDocument();
      expect(within(popover).getByText('This issue has basic information but could be improved.')).toBeInTheDocument();
    });
  });

  describe('interaction behavior', () => {
    describe('on large screens', () => {
      beforeEach(() => {
        window.matchMedia = createMatchMedia(1024);
        mockUseMediaQuery.mockReturnValue(true);
      });

      it('opens popover on pointer enter', async () => {
        render(
          <IssueQualityIndicator qualityScore={15} />,
          { messages: { 'issue.list.qualityIndicators': mockMessages } }
        );

        const container = screen.getByRole('button').parentElement;
        await userEvent.hover(container!);

        expect(await screen.findByRole('dialog')).toBeInTheDocument();
      });

      it('closes popover on pointer leave', async () => {
        render(
          <IssueQualityIndicator qualityScore={15} />,
          { messages: { 'issue.list.qualityIndicators': mockMessages } }
        );

        const container = screen.getByRole('button').parentElement;
        await userEvent.hover(container!);
        expect(await screen.findByRole('dialog')).toBeInTheDocument();

        await userEvent.unhover(container!);
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });

      it('toggles popover on click', async () => {
        render(
          <IssueQualityIndicator qualityScore={15} />,
          { messages: { 'issue.list.qualityIndicators': mockMessages } }
        );

        const button = screen.getByRole('button');

        // First click opens
        await userEvent.click(button);
        expect(await screen.findByRole('dialog')).toBeInTheDocument();

        // Second click closes
        await userEvent.click(button);
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    describe('on small screens', () => {
      beforeEach(() => {
        window.matchMedia = createMatchMedia(640);
        mockUseMediaQuery.mockReturnValue(false);
      });

      it('does not open popover on pointer enter', async () => {
        render(
          <IssueQualityIndicator qualityScore={15} />,
          { messages: { 'issue.list.qualityIndicators': mockMessages } }
        );

        const container = screen.getByRole('button').parentElement;
        await userEvent.hover(container!);

        // Wait a bit to ensure popover doesn't appear
        await new Promise(resolve => setTimeout(resolve, 100));
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });

      it('opens popover on click', async () => {
        render(
          <IssueQualityIndicator qualityScore={15} />,
          { messages: { 'issue.list.qualityIndicators': mockMessages } }
        );

        const button = screen.getByRole('button');
        await userEvent.click(button);

        expect(await screen.findByRole('dialog')).toBeInTheDocument();
      });
    });
  });

  describe('event handling', () => {
    it('prevents default and stops propagation on click', async () => {
      const handleClick = jest.fn();
      render(
        <div onClick={handleClick}>
          <IssueQualityIndicator qualityScore={15} />
        </div>,
        { messages: { 'issue.list.qualityIndicators': mockMessages } }
      );

      const button = screen.getByRole('button');
      await userEvent.click(button);

      // Parent click handler should not be called due to stopPropagation
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe('accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <IssueQualityIndicator qualityScore={15} />,
        { messages: { 'issue.list.qualityIndicators': mockMessages } }
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Issue quality: Not useful');
      expect(button).toHaveAttribute('aria-describedby', 'quality-popover-content');
      expect(button).toHaveAttribute('type', 'button');
    });

    it('has focus styles', () => {
      render(
        <IssueQualityIndicator qualityScore={15} />,
        { messages: { 'issue.list.qualityIndicators': mockMessages } }
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('focus:outline-none', 'focus:ring-2', 'focus:ring-blue-500', 'focus:ring-offset-1');
    });
  });
});